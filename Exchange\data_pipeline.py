#!/usr/bin/env python3
"""
Automated Data Processing Pipeline

This module provides a comprehensive pipeline for processing data files based on
Excel-defined transformation rules.

Architecture:
- MasterExcelReader: Reads the master Excel workbook and extracts file list and transformation rules
- FileDetector: Detects file types and determines appropriate delimiters
- DataReader: Reads various file formats (.txt, .dat, .csv) with encoding detection
- TransformationEngine: Applies header mappings, filtering, and data transformations
- OutputGenerator: Creates cleaned Excel files with proper formatting
- Logger: Comprehensive logging and error tracking
"""

import pandas as pd
import numpy as np
import openpyxl
import chardet
import csv
import re
import logging
from pathlib import Path
import io # Added for StringIO
from collections import Counter # Added for column count analysis

from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Use INFO level for production
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pipeline.log'),
        logging.StreamHandler()
    ]
)


@dataclass
class ReplacementRule:
    """Rule for replacing a value with another"""
    value: str
    replace_with: str

class FileType(Enum):
    """Supported file types"""
    TXT = "txt"
    DAT = "dat"
    CSV = "csv"

class TransformationType(Enum):
    """Types of data transformations"""
    DATE_CONVERSION = "date_conversion"
    DIVIDE_BY_100 = "divide_by_100"
    TEXT_FORMAT = "text_format"
    NUMBER_FORMAT = "number_format"

@dataclass
class FileInfo:
    """Information about a file to be processed"""
    sr_number: str
    file_name: str
    file_location: str
    file_date_time: str = ""
    macro_yn: str = ""
    status: str = ""
    remark: str = ""
    last_update_time: str = ""
    priority: str = ""
    exchange: str = ""
    historical_use: str = ""
    action_required: str = "" # New field for "Action Required" from Sheet1

@dataclass
class TransformationRule:
    """Rule for transforming a column"""
    header_name: str
    required: bool  # True if Y, False if -
    format_type: str
    special_action: str
    example: str = ""
    action_required: str = ""  # Added to store Action Required value for this header

@dataclass
class ProcessingResult:
    """Result of processing a single file"""
    sr_number: str
    file_name: str
    status: str  # Success/Failed
    errors: List[str]
    columns_dropped: List[str]
    columns_transformed: List[str]
    timestamp: datetime
    output_file: Optional[str] = None


class MasterExcelReader:
    """Reads and parses the master Excel workbook"""

    def __init__(self, excel_path: str):
        self.excel_path = Path(excel_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.replacement_map: Dict[str, str] = {} # To store replacement rules

    def read_file_list(self) -> List[FileInfo]:
        """Read the file list from Sheet1"""
        try:
            # Use data_only=True to get evaluated cell values instead of formulas
            wb = openpyxl.load_workbook(self.excel_path, read_only=True, data_only=True)
            ws = wb['Sheet1']

            files = []
            # Find the header row (contains 'Sr. No.', 'File Name', etc.)
            header_row = None
            for row_num in range(1, 20):  # Check first 20 rows
                cell_value = ws.cell(row=row_num, column=1).value
                if cell_value and 'Sr. No.' in str(cell_value):
                    header_row = row_num
                    break

            if not header_row:
                raise ValueError("Could not find header row in Sheet1")

            # Read headers
            headers = []
            # Assuming 'Action Required' is the 12th column (index 11)
            for col_num in range(1, 13): # Read up to 12 columns
                cell_value = ws.cell(row=header_row, column=col_num).value
                headers.append(str(cell_value) if cell_value else f"Col_{col_num}")

            # Read data rows
            for row_num in range(header_row + 1, ws.max_row + 1):
                row_data = []
                for col_num in range(1, len(headers) + 1):
                    cell_value = ws.cell(row=row_num, column=col_num).value
                    row_data.append(str(cell_value) if cell_value else "")

                # Skip empty rows
                if not any(val.strip() for val in row_data):
                    continue

                # Process SR number to ensure it's a clean integer string
                sr_number_raw = row_data[0] if len(row_data) > 0 else ""
                sr_number = self._extract_sr_number(sr_number_raw)

                # Create FileInfo object
                file_info = FileInfo(
                    sr_number=sr_number,
                    file_name=row_data[1] if len(row_data) > 1 else "",
                    file_location=row_data[2] if len(row_data) > 2 else "",
                    file_date_time=row_data[3] if len(row_data) > 3 else "",
                    macro_yn=row_data[4] if len(row_data) > 4 else "",
                    status=row_data[5] if len(row_data) > 5 else "",
                    remark=row_data[6] if len(row_data) > 6 else "",
                    last_update_time=row_data[7] if len(row_data) > 7 else "",
                    priority=row_data[8] if len(row_data) > 8 else "",
                    exchange=row_data[9] if len(row_data) > 9 else "",
                    historical_use=row_data[10] if len(row_data) > 10 else "",
                    action_required=row_data[11] if len(row_data) > 11 else "" # Read "Action Required"
                )

                files.append(file_info)

            wb.close()
            self.logger.info(f"Read {len(files)} files from Sheet1")
            return files

        except Exception as e:
            self.logger.error(f"Error reading file list: {e}")
            raise

    def read_transformation_rules(self, sr_number: str) -> List[TransformationRule]:
        """Read transformation rules for a specific SR number"""
        try:
            # Try different possible sheet name formats
            possible_formats = [
                f"Sr. No. {sr_number}",
                f"SR {sr_number}",
                str(sr_number),  # Just the number
                f"SR{sr_number}",  # No space
                f"SR.{sr_number}",  # With dot
                f"SR NO {sr_number}",  # Alternative format
                f"SR NO. {sr_number}"  # With dot
            ]
            
            wb = openpyxl.load_workbook(self.excel_path, read_only=True, data_only=True)
            sheet_name = None
            
            # Try to find the sheet using any of the possible formats
            for fmt in possible_formats:
                if fmt in wb.sheetnames:
                    sheet_name = fmt
                    break
            
            if not sheet_name:
                # If no exact match found, try case-insensitive matching
                sheet_name_lower = str(sr_number).lower()
                for ws_name in wb.sheetnames:
                    if sheet_name_lower in ws_name.lower():
                        sheet_name = ws_name
                        break

            if not sheet_name:
                self.logger.warning(f"Could not find sheet for SR number {sr_number}. Tried formats: {', '.join(possible_formats)}")
                wb.close()
                return []
            
            self.logger.info(f"Found SR sheet with format: {sheet_name}")
            ws = wb[sheet_name]
            rules = []

            # Find the header row (contains 'Header Name', 'Required Y/N', etc.)
            header_row = None
            for row_num in range(1, 10):
                cell_value = ws.cell(row=row_num, column=1).value
                if cell_value and 'Header Name' in str(cell_value):
                    header_row = row_num
                    break

            if not header_row:
                self.logger.warning(f"Could not find header row in {sheet_name}")
                wb.close()
                return []

            # Find the Special Action column
            special_action_col = None
            header_cols = {}
            
            # Log all headers for debugging
            self.logger.info(f"Scanning headers in row {header_row} for Special Action column")
            
            # First scan all columns to map headers
            for col_num in range(1, ws.max_column + 1):
                cell_value = str(ws.cell(row=header_row, column=col_num).value or '').strip()
                if not cell_value:  # Stop if we hit an empty column
                    break
                header_cols[cell_value.lower()] = col_num
                self.logger.debug(f"Found header in column {col_num}: '{cell_value}'")

            # Look for Special Action column variations (case-insensitive)
            special_action_variants = [
                'special action',
                'specialaction',
                'sp action',
                'special_action',
                'sp_action',
                'spec action',
                'action',
                'sp'          # Short form
            ]
            self.logger.debug(f"Searching for Special Action column using variants: {special_action_variants}")
            
            # Try to find any matching variant
            for variant in special_action_variants:
                for header, col in header_cols.items():
                    if variant in header.lower().replace(' ', ''):
                        special_action_col = col
                        self.logger.info(f"Found Special Action column (variant: {variant}) at position {col}")
                        break
                if special_action_col:
                    break
            
            if not special_action_col:
                self.logger.warning(f"Could not find Special Action column in sheet {sheet_name}")
                self.logger.debug(f"Available headers: {', '.join(header_cols.keys())}")

            # Read transformation rules
            for row_num in range(header_row + 1, ws.max_row + 1):
                header_name = ws.cell(row=row_num, column=1).value
                required_yn = ws.cell(row=row_num, column=2).value
                format_type = ws.cell(row=row_num, column=3).value
                special_action = ws.cell(row=row_num, column=4).value
                example = ws.cell(row=row_num, column=5).value

                # Skip empty rows
                if not header_name:
                    continue

                # Read Action Required value from the found column (if it exists)
                action_required = ""
                if special_action_col:
                    special_action = str(ws.cell(row=row_num, column=special_action_col).value or '').strip().lower()
                    self.logger.debug(f"Special Action value for {header_name}: '{special_action}'")
                    if special_action:
                        self.logger.info(f"Column {header_name} has special action: '{special_action}'")

                rule = TransformationRule(
                    header_name=str(header_name).strip(),
                    required=str(required_yn).strip().upper() == 'Y',
                    format_type=str(format_type) if format_type else "",
                    special_action=str(special_action) if special_action else "",
                    example=str(example) if example else "",
                    action_required=str(action_required).strip().lower() if action_required else ""
                )

                rules.append(rule)

            wb.close()
            self.logger.info(f"Read {len(rules)} transformation rules for SR {sr_number}")
            return rules

        except Exception as e:
            self.logger.error(f"Error reading transformation rules for SR {sr_number}: {e}")
            return []

    def _extract_sr_number(self, raw_value: str) -> str:
        """Extract and clean SR number from raw cell value"""
        try:
            if not raw_value or raw_value.strip() == "":
                return ""

            # Handle numeric values (including floats from Excel)
            try:
                # Try to convert to float first, then to int to handle Excel's numeric format
                numeric_value = float(raw_value)
                if numeric_value == int(numeric_value):  # Check if it's a whole number
                    return str(int(numeric_value))
                else:
                    return str(int(numeric_value))  # Round down if it's a decimal
            except (ValueError, TypeError):
                pass

            # Handle string values - extract numbers
            import re
            numbers = re.findall(r'\d+', str(raw_value))
            if numbers:
                return numbers[0]  # Return the first number found

            # If no numbers found, return the original value cleaned
            return str(raw_value).strip()

        except Exception as e:
            self.logger.warning(f"Error extracting SR number from '{raw_value}': {e}")
            return str(raw_value) if raw_value else ""

    def read_replacement_rules(self, sheet_name: str = "Replace_Rules") -> Dict[str, str]:
        """Read replacement rules from a specified sheet and store them in a dictionary"""
        try:
            wb = openpyxl.load_workbook(self.excel_path, read_only=True, data_only=True)

            if sheet_name not in wb.sheetnames:
                self.logger.warning(f"Replacement sheet '{sheet_name}' not found. No word replacements will be applied.")
                wb.close()
                return {}

            ws = wb[sheet_name]
            replacement_rules = {}

            # Find the header row (contains 'value', 'replace_with')
            header_row = None
            for row_num in range(1, 10): # Check first 10 rows
                cell_value_col1 = str(ws.cell(row=row_num, column=1).value or '').strip().lower()
                cell_value_col2 = str(ws.cell(row=row_num, column=2).value or '').strip().lower()
                if 'value' in cell_value_col1 and 'replace_with' in cell_value_col2:
                    header_row = row_num
                    break

            if not header_row:
                self.logger.warning(f"Could not find header row ('value', 'replace_with') in sheet '{sheet_name}'. No word replacements will be applied.")
                wb.close()
                return {}

            self.logger.info(f"Reading replacement rules from sheet '{sheet_name}', header row: {header_row}")
            
            # Read data rows
            for row_num in range(header_row + 1, ws.max_row + 1):
                value = ws.cell(row=row_num, column=1).value
                replace_with = ws.cell(row=row_num, column=2).value

                # Skip empty rows or rows with empty 'value'
                if not value or str(value).strip() == "":
                    self.logger.debug(f"Skipping empty row {row_num}")
                    continue

                value_str = str(value).strip()
                replace_with_str = str(replace_with).strip() if replace_with is not None else ""
                self.logger.debug(f"Found replacement rule: '{value_str}' -> '{replace_with_str}'")

                replacement_rules[str(value).strip()] = str(replace_with).strip() if replace_with is not None else ""

            wb.close()
            self.logger.info(f"Read {len(replacement_rules)} replacement rules from '{sheet_name}'")
            self.replacement_map = replacement_rules # Store the map
            return replacement_rules

        except Exception as e:
            self.logger.error(f"Error reading replacement rules from '{sheet_name}': {e}")
            return {}


class FileDetector:
    """Detects file types and determines appropriate delimiters"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def detect_file_type(self, file_path: Path) -> FileType:
        """Detect file type based on extension"""
        extension = file_path.suffix.lower().lstrip('.')

        if extension in ['txt']:
            return FileType.TXT
        elif extension in ['dat']:
            return FileType.DAT
        elif extension in ['csv']:
            return FileType.CSV
        else:
            # Default to TXT for unknown extensions
            self.logger.warning(f"Unknown file extension '{extension}', treating as TXT")
            return FileType.TXT

    def detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Read first 10KB
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']

                self.logger.info(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")

                # Fallback to common encodings if confidence is low
                if confidence < 0.7:
                    for fallback in ['utf-8', 'latin-1', 'cp1252']:
                        try:
                            with open(file_path, 'r', encoding=fallback) as test_file:
                                test_file.read(1000)
                            self.logger.info(f"Using fallback encoding: {fallback}")
                            return fallback
                        except UnicodeDecodeError:
                            continue

                return encoding or 'utf-8'

        except Exception as e:
            self.logger.warning(f"Error detecting encoding: {e}, using utf-8")
            return 'utf-8'

    def detect_delimiter(self, file_path: Path, encoding: str = 'utf-8') -> str:
        """Detect the delimiter used in the file"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                # Read first few lines to detect delimiter
                sample_lines = []
                for i, line in enumerate(f):
                    if i >= 5:  # Read max 5 lines
                        break
                    sample_lines.append(line.strip())

            if not sample_lines:
                return ','  # Default delimiter

            # Test common delimiters
            delimiters = ['|', ',', '\t', ';', ' ']
            delimiter_scores = {}

            for delimiter in delimiters:
                scores = []
                for line in sample_lines:
                    if line:
                        parts = line.split(delimiter)
                        scores.append(len(parts))

                if scores:
                    # Filter out outliers (like header lines) by removing scores that are too different
                    if len(scores) > 1:
                        # Remove the most extreme outlier if it's significantly different
                        sorted_scores = sorted(scores)
                        if len(sorted_scores) >= 3:
                            # If the smallest score is much smaller than the median, remove it
                            median_score = sorted_scores[len(sorted_scores)//2]
                            if sorted_scores[0] < median_score * 0.3:  # Less than 30% of median
                                scores = [s for s in scores if s != sorted_scores[0]]

                    # Check consistency of column count
                    avg_cols = sum(scores) / len(scores)
                    if max(scores) > 1:  # Only consider if it actually splits the data
                        consistency = 1.0 - (max(scores) - min(scores)) / max(1, avg_cols)
                        delimiter_scores[delimiter] = (avg_cols, consistency)

            # Choose delimiter with highest column count and consistency
            best_delimiter = ','
            best_score = (0, 0)

            for delimiter, (cols, consistency) in delimiter_scores.items():
                # Prioritize consistency, then column count
                score = (consistency, cols)
                if score > best_score and cols > 1:
                    best_score = score
                    best_delimiter = delimiter

            self.logger.info(f"Detected delimiter: '{best_delimiter}' (avg cols: {best_score[1]:.1f}, consistency: {best_score[0]:.2f})")
            return best_delimiter

        except Exception as e:
            self.logger.warning(f"Error detecting delimiter: {e}, using comma")
            return ','


class DataReader:
    """Reads various file formats with encoding and delimiter detection"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.file_detector = FileDetector()

    def read_file(self, file_path: str) -> pd.DataFrame:
        """Read a file and return as DataFrame"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Detect file properties
        file_type = self.file_detector.detect_file_type(file_path)
        encoding = self.file_detector.detect_encoding(file_path)
        delimiter = self.file_detector.detect_delimiter(file_path, encoding)

        self.logger.info(f"Reading {file_type.value} file: {file_path}")
        self.logger.info(f"Using encoding: {encoding}, delimiter: '{delimiter}'")

        try:
            # For files with inconsistent column counts (TXT, DAT), use line-by-line reading
            if file_type in [FileType.TXT, FileType.DAT]:
                df = self._read_inconsistent_file(file_path, delimiter, encoding)
            else:
                # For CSV files, attempt standard pandas reading first
                try:
                    df = pd.read_csv(
                        file_path,
                        delimiter=delimiter,
                        encoding=encoding,
                        header=None,  # We'll handle headers separately
                        dtype=str,    # Read everything as string initially
                        na_filter=False,  # Don't convert to NaN
                        quoting=csv.QUOTE_MINIMAL
                    )
                    self.logger.info(f"Successfully read CSV file with standard method.")

                except pd.errors.ParserError as e:
                    self.logger.warning(f"ParserError encountered while reading {file_path}: {e}")
                    self.logger.info("Attempting to fix inconsistent column counts...")

                    # Read file line by line to analyze and fix
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            lines = f.readlines()

                        if not lines:
                            raise ValueError("File is empty.")

                        # Analyze column counts in the first few lines
                        line_column_counts = []
                        sample_size = min(len(lines), 10) # Analyze first 10 lines
                        for i in range(sample_size):
                            line = lines[i].strip()
                            if line:
                                line_column_counts.append(len(line.split(delimiter)))

                        if not line_column_counts:
                             raise ValueError("Could not determine column counts from sample lines.")

                        # Determine the most frequent column count (excluding the first line initially)
                        if len(line_column_counts) > 1:
                            # Count occurrences of column counts from the 2nd line onwards
                            counts = Counter(line_column_counts[1:])
                            if counts:
                                most_common_count = counts.most_common(1)[0][0]
                            else:
                                # If only one line or all lines after first are empty, use first line count
                                most_common_count = line_column_counts[0]
                        else:
                             # If only one line, that's the most common count
                             most_common_count = line_column_counts[0]


                        self.logger.info(f"Most common column count (excluding header): {most_common_count}")

                        corrected_lines = []
                        header_fixed = False

                        # Process the header line (first line)
                        header_line = lines[0].strip()
                        header_parts = header_line.split(delimiter)
                        header_col_count = len(header_parts)

                        if header_col_count != most_common_count:
                            self.logger.warning(f"Header row has {header_col_count} columns, expected {most_common_count}. Attempting to adjust header.")
                            if header_col_count > most_common_count:
                                # Truncate header if it has too many columns
                                corrected_header_parts = header_parts[:most_common_count]
                                self.logger.info(f"Truncated header from {header_col_count} to {most_common_count} columns.")
                            else:
                                # Pad header if it has too few columns
                                corrected_header_parts = header_parts + [''] * (most_common_count - header_col_count)
                                self.logger.info(f"Padded header from {header_col_count} to {most_common_count} columns.")

                            corrected_lines.append(delimiter.join(corrected_header_parts) + '\n')
                            header_fixed = True
                        else:
                            # Header count matches, keep it as is
                            corrected_lines.append(lines[0])


                        # Process the rest of the lines - pad/truncate to match most_common_count
                        for line_num in range(1, len(lines)):
                             line = lines[line_num].strip()
                             if not line:
                                 corrected_lines.append('\n') # Keep empty lines
                                 continue

                             parts = line.split(delimiter)
                             current_col_count = len(parts)

                             if current_col_count != most_common_count:
                                 # Pad or truncate data rows to match the most common count
                                 if current_col_count < most_common_count:
                                     corrected_parts = parts + [''] * (most_common_count - current_col_count)
                                     # self.logger.debug(f"Padded line {line_num + 1} from {current_col_count} to {most_common_count} columns.")
                                 else: # current_col_count > most_common_count
                                     corrected_parts = parts[:most_common_count]
                                     # self.logger.debug(f"Truncated line {line_num + 1} from {current_col_count} to {most_common_count} columns.")
                                 corrected_lines.append(delimiter.join(corrected_parts) + '\n')
                             else:
                                 # Line already has the correct number of columns
                                 corrected_lines.append(lines[line_num])


                        # Re-read the corrected content into a DataFrame
                        corrected_content = "".join(corrected_lines)
                        df = pd.read_csv(
                            io.StringIO(corrected_content),
                            delimiter=delimiter,
                            encoding=encoding,
                            header=None,
                            dtype=str,
                            na_filter=False,
                            quoting=csv.QUOTE_MINIMAL
                        )
                        self.logger.info("Successfully re-read file after fixing column counts.")

                    except Exception as fix_e:
                        self.logger.error(f"Failed to fix and re-read file {file_path}: {fix_e}")
                        # If fixing fails, re-raise the original parser error
                        raise e from fix_e


            # Check for and remove duplicate headers (applies to both initial read and fixed read)
            df = self._remove_duplicate_headers(df)

            self.logger.info(f"Successfully read file: {df.shape[0]} rows x {df.shape[1]} columns")
            return df

        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            raise

    def _read_inconsistent_file(self, file_path: Path, delimiter: str, encoding: str) -> pd.DataFrame:
        """Read files with inconsistent column counts line by line"""

        try:
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()

            # Parse each line
            all_rows = []
            max_columns = 0

            for line_num, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue

                # Split by delimiter
                row = line.split(delimiter)
                all_rows.append(row)
                max_columns = max(max_columns, len(row))

            # Pad all rows to have the same number of columns
            for row in all_rows:
                while len(row) < max_columns:
                    row.append('')

            # Create DataFrame
            df = pd.DataFrame(all_rows, dtype=str)

            self.logger.info(f"Read {len(df)} rows with {max_columns} columns (padded)")
            return df

        except Exception as e:
            self.logger.error(f"Error reading inconsistent file: {e}")
            raise

    def _remove_duplicate_headers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate headers if the first two rows are identical"""
        try:
            if df.empty or len(df) < 2:
                return df

            # Get the first two rows as lists for comparison
            first_row = df.iloc[0].astype(str).tolist()
            second_row = df.iloc[1].astype(str).tolist()

            # Check if the first two rows are identical
            if first_row == second_row:
                self.logger.info("Detected duplicate headers - removing the second row")
                # Remove the second row (index 1)
                df_cleaned = df.drop(df.index[1]).reset_index(drop=True)
                return df_cleaned
            else:
                # No duplicate headers detected
                return df

        except Exception as e:
            self.logger.warning(f"Error checking for duplicate headers: {e}")
            return df


class TransformationEngine:
    """Applies header mappings, filtering, and data transformations"""

    def __init__(self, replacement_map: Dict[str, str] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.replacement_map = replacement_map if replacement_map is not None else {}

    def apply_transformations(self, df: pd.DataFrame, rules: List[TransformationRule],
                             file_info: FileInfo = None) -> Tuple[pd.DataFrame, List[str], List[str]]:
        """Apply transformation rules to the DataFrame"""

        if df.empty or not rules:
            return df, [], []

        columns_dropped = []
        columns_transformed = []

        try:
            # STEP 1: First, ensure we have enough columns to match all rules (required + non-required)
            total_rules = len(rules)
            while df.shape[1] < total_rules:
                df[f'Col_{df.shape[1]}'] = ''

            # STEP 2: Apply headers to ALL columns first (both required and non-required)
            # This ensures proper alignment between data and headers
            all_headers = [rule.header_name for rule in rules]
            df_with_headers = df.iloc[:, :len(all_headers)].copy()
            df_with_headers.columns = all_headers

            # STEP 3: Apply transformations to ALL columns (before filtering)
            for rule in rules:
                if rule.header_name in df_with_headers.columns:
                    # First check if this column needs word replacement
                    self.logger.debug(f"Checking action_required for column {rule.header_name}: '{rule.action_required}'")
                    if str(rule.action_required).strip().lower() == 'replace':
                        self.logger.info(f"Applying word replacement for column: {rule.header_name}")
                        if not self.replacement_map:
                            self.logger.warning(f"No replacement rules loaded for column {rule.header_name}")
                        else:
                            self.logger.info(f"Found {len(self.replacement_map)} replacement rules to apply to {rule.header_name}")
                        df_with_headers[rule.header_name] = self._apply_word_replacement_to_series(
                            df_with_headers[rule.header_name], rule.header_name
                        )
                        columns_transformed.append(f"{rule.header_name} (word replacement)")
                    
                    # Then apply other transformations
                    df_with_headers[rule.header_name] = self._apply_column_transformation(
                        df_with_headers[rule.header_name], rule
                    )

                    if rule.special_action and rule.special_action != '-':
                        columns_transformed.append(f"{rule.header_name} ({rule.special_action})")

            # STEP 4: Now filter to keep only required columns (Y flag)
            required_columns = []
            for rule in rules:
                if rule.required:
                    required_columns.append(rule.header_name)
                else:
                    columns_dropped.append(rule.header_name)

            # STEP 5: Create final DataFrame with only required columns
            df_transformed = df_with_headers[required_columns].copy()

            # STEP 6: Add Exchange column as the first column if file_info is provided
            if file_info and file_info.exchange:
                # Check if Exchange column already exists
                if 'Exchange' not in df_transformed.columns:
                    # Create Exchange column with the same value for all rows
                    exchange_column = pd.Series([file_info.exchange] * len(df_transformed), name='Exchange')

                    # Insert Exchange column as the first column
                    df_transformed.insert(0, 'Exchange', exchange_column)

                    self.logger.info(f"Added Exchange column with value: {file_info.exchange}")
                else:
                    # Update existing Exchange column with the correct value
                    df_transformed['Exchange'] = file_info.exchange

                    # Move Exchange column to first position if it's not already there
                    if df_transformed.columns[0] != 'Exchange':
                        cols = df_transformed.columns.tolist()
                        cols.remove('Exchange')
                        cols.insert(0, 'Exchange')
                        df_transformed = df_transformed[cols]

                    self.logger.info(f"Updated existing Exchange column with value: {file_info.exchange}")

            # STEP 7: Enforce format validation and conversion based on Format column
            format_conversions = []
            for rule in rules:
                if rule.required and rule.format_type:
                    format_type = rule.format_type.lower().strip()
                    if format_type in ['text', 'number', 'number (%)', 'date']:
                        df_transformed[rule.header_name] = self._enforce_column_format(
                            df_transformed[rule.header_name], rule.header_name, format_type
                        )
                        format_conversions.append(f"{rule.header_name} -> {format_type}")

            if format_conversions:
                self.logger.info(f"Applied format conversions: {', '.join(format_conversions)}")

            self.logger.info(f"Applied transformations: {len(columns_transformed)} columns transformed, {len(columns_dropped)} columns dropped")
            self.logger.info(f"Final columns: {list(df_transformed.columns)}")
            return df_transformed, columns_dropped, columns_transformed

        except Exception as e:
            self.logger.error(f"Error applying transformations: {e}")
            raise

    def _apply_column_transformation(self, series: pd.Series, rule: TransformationRule) -> pd.Series:
        """Apply transformation to a specific column"""

        if not rule.special_action or rule.special_action == '-':
            return series

        try:
            special_action = rule.special_action.lower()

            # Date conversion (Unix timestamp to DD-MMM-YY)
            if 'convert to date' in special_action or 'date' in special_action:
                return self._convert_unix_to_date(series)

            # Divide by 100
            elif 'divide by 100' in special_action or 'devide by 100' in special_action:
                return self._divide_by_100(series)

            # Number formatting
            elif rule.format_type.lower() == 'number':
                return self._format_as_number(series)

            # Text formatting
            elif rule.format_type.lower() == 'text':
                return series.astype(str)

            else:
                self.logger.warning(f"Unknown transformation: {rule.special_action}")
                return series

        except Exception as e:
            self.logger.warning(f"Error transforming column {rule.header_name}: {e}")
            return series

    def _convert_unix_to_date(self, series: pd.Series) -> pd.Series:
        """Convert Unix timestamp to DD-MMM-YY format using Excel's FLOOR logic"""
        def convert_timestamp(value):
            try:
                if pd.isna(value) or value == '' or str(value).strip() == '':
                    return ''

                # Skip non-numeric values
                value_str = str(value).strip()
                if not value_str.replace('.', '').replace('-', '').isdigit():
                    return value_str  # Return as-is if not numeric

                # Convert to numeric value
                unix_val = int(float(value_str))

                # Handle zero or invalid timestamps
                if unix_val == 0:
                    return ''

                # Implement Excel formula: FLOOR(unix_val/60/60/24,1) + DATE(1980,1,1)
                # This converts Unix timestamp (seconds) to days and adds to 1980-01-01
                days_since_unix_epoch = int(unix_val // 86400)  # 86400 = 60*60*24 (FLOOR operation)

                # Excel's DATE(1980,1,1) as base date
                base_date = datetime(1980, 1, 1)
                target_date = base_date + timedelta(days=days_since_unix_epoch)

                # Format as DD-MMM-YY
                return target_date.strftime('%d-%b-%y').upper()

            except (ValueError, TypeError, OverflowError):
                return str(value)  # Return original value if conversion fails

        return series.apply(convert_timestamp)

    def _divide_by_100(self, series: pd.Series) -> pd.Series:
        """Divide numeric values by 100"""
        def divide_value(value):
            try:
                if pd.isna(value) or value == '':
                    return ''

                numeric_value = float(str(value))
                result = numeric_value / 100

                # Format with 2 decimal places if needed
                if result == int(result):
                    return str(int(result))
                else:
                    return f"{result:.2f}"

            except (ValueError, TypeError):
                return str(value)  # Return original value if conversion fails

        return series.apply(divide_value)

    def _format_as_number(self, series: pd.Series) -> pd.Series:
        """Format values as numbers"""
        def format_number(value):
            try:
                if pd.isna(value) or value == '':
                    return ''

                numeric_value = float(str(value))

                # Return as integer if it's a whole number
                if numeric_value == int(numeric_value):
                    return str(int(numeric_value))
                else:
                    return str(numeric_value)

            except (ValueError, TypeError):
                return str(value)  # Return original value if conversion fails

        return series.apply(format_number)

    def _enforce_column_format(self, series: pd.Series, column_name: str, format_type: str) -> pd.Series:
        """Enforce specific format type (Text/Number/Percentage/Date) on a column"""
        try:
            if format_type == 'text':
                # Convert to text format
                return series.astype(str)

            elif format_type == 'number':
                # Convert to numeric format
                def convert_to_number(value):
                    try:
                        if pd.isna(value) or value == '' or value == '-':
                            return value  # Keep blanks and hyphens as-is

                        # Try to convert to numeric
                        numeric_value = pd.to_numeric(str(value), errors='coerce')

                        # If conversion failed, return original value
                        if pd.isna(numeric_value):
                            return str(value)

                        # Return as string representation of number for consistency
                        if numeric_value == int(numeric_value):
                            return str(int(numeric_value))
                        else:
                            return str(numeric_value)

                    except:
                        return str(value)  # Return as string if any error

                return series.apply(convert_to_number)

            elif format_type == 'number (%)':
                # Convert to percentage format
                def convert_to_percentage(value):
                    try:
                        if pd.isna(value) or value == '' or value == '-':
                            return value  # Keep blanks and hyphens as-is

                        # Try to convert to numeric first
                        numeric_value = pd.to_numeric(str(value), errors='coerce')

                        # If conversion failed, return original value
                        if pd.isna(numeric_value):
                            return str(value)

                        # Convert to percentage (multiply by 100 if value is between 0 and 1)
                        if 0 <= numeric_value <= 1:
                            percentage_value = numeric_value * 100
                        else:
                            percentage_value = numeric_value

                        # Return as string with % symbol
                        return f"{percentage_value:.2f}%"

                    except:
                        return str(value)  # Return as string if any error

                return series.apply(convert_to_percentage)

            elif format_type == 'date':
                # Date format is already handled by special actions, just ensure string format
                return series.astype(str)

            else:
                self.logger.warning(f"Unknown format type '{format_type}' for column '{column_name}'")
                return series

        except Exception as e:
            self.logger.warning(f"Error enforcing format '{format_type}' on column '{column_name}': {e}")
            return series

    def _apply_word_replacement_to_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply word replacement to all string columns in the DataFrame."""
        if not self.replacement_map:
            self.logger.warning("No replacement rules loaded. Skipping word replacement for the entire DataFrame.")
            return df

        df_copy = df.copy()
        for column_name in df_copy.columns:
            # Only apply to columns that are of object type (typically strings)
            if df_copy[column_name].dtype == 'object':
                df_copy[column_name] = self._apply_word_replacement_to_series(df_copy[column_name], column_name)
        return df_copy

    def _apply_word_replacement_to_series(self, series: pd.Series, column_name: str) -> pd.Series:
        """Apply word replacement based on the loaded replacement map to a single series."""
        def replace_value(value):
            if pd.isna(value) or str(value).strip() == "":
                return value

            value_str = str(value).strip()
            if value_str in self.replacement_map:
                return self.replacement_map[value_str]
            else:
                # Log a warning if the value is not found in the replacement map
                self.logger.warning(f"Replacement Error: Value '{value_str}' not found in Replace Sheet for column '{column_name}'. Please check and update the Replace Sheet accordingly.")
                return value # Return original value if not found

        return series.apply(replace_value)


class OutputGenerator:
    """Creates cleaned Excel files with proper formatting"""

    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(self.__class__.__name__)

    def generate_output_file(self, df: pd.DataFrame, file_info: FileInfo,
                           columns_dropped: List[str], columns_transformed: List[str],
                           format_info: dict = None) -> str:
        """Generate cleaned Excel file"""

        try:
            # Create a copy of the DataFrame to avoid modifying the original
            df_clean = df.copy()

            # STEP 1: Check for and remove duplicate headers in output
            df_clean = self._remove_duplicate_headers_from_output(df_clean)

            # STEP 2: Replace all blank values with hyphens
            # Handle both empty strings and NaN values
            df_clean = df_clean.replace('', '-')  # Replace empty strings
            df_clean = df_clean.fillna('-')       # Replace NaN values

            # Also handle whitespace-only strings
            df_clean = df_clean.replace(r'^\s*$', '-', regex=True)

            self.logger.info(f"Replaced blank values with '-' in final output")

            # Create output filename
            base_name = Path(file_info.file_name).stem
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{base_name}_cleaned_{timestamp}.xlsx"
            output_path = self.output_dir / output_filename

            # Create Excel writer with formatting
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Write main data (using cleaned DataFrame)
                df_clean.to_excel(writer, sheet_name='Data', index=False)

                # Write metadata sheet
                metadata = {
                    'Original File': [file_info.file_name],
                    'Original Location': [file_info.file_location],
                    'SR Number': [file_info.sr_number],
                    'Processing Time': [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                    'Total Rows': [len(df_clean)],
                    'Total Columns': [len(df_clean.columns)],
                    'Columns Dropped': [', '.join(columns_dropped) if columns_dropped else 'None'],
                    'Columns Transformed': [', '.join(columns_transformed) if columns_transformed else 'None']
                }

                metadata_df = pd.DataFrame(metadata)
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)

                # Format the Data sheet
                workbook = writer.book
                data_sheet = writer.sheets['Data']

                # Auto-adjust column widths
                for column in data_sheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                    data_sheet.column_dimensions[column_letter].width = adjusted_width

                # Format header row
                from openpyxl.styles import Font, PatternFill
                header_font = Font(bold=True)
                header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                for cell in data_sheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill

                # Apply format-specific cell formatting
                if format_info:
                    self._apply_excel_cell_formatting(data_sheet, df_clean, format_info)

            self.logger.info(f"Generated output file: {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"Error generating output file: {e}")
            raise

    def _apply_excel_cell_formatting(self, worksheet, df: pd.DataFrame, format_info: dict):
        """Apply Excel cell formatting based on format specifications"""
        try:
            # Get column headers
            headers = list(df.columns)

            # Apply formatting to each column based on format_info
            for col_idx, header in enumerate(headers):
                if header in format_info:
                    format_type = format_info[header].lower().strip()
                    col_letter = chr(65 + col_idx)  # Convert to Excel column letter (A, B, C, ...)

                    # Apply formatting to data rows (skip header row)
                    for row_idx in range(2, len(df) + 2):  # Start from row 2 (after header)
                        cell = worksheet[f"{col_letter}{row_idx}"]
                        cell_value = df.iloc[row_idx - 2, col_idx]  # Get DataFrame value

                        # Skip hyphen values (our blank replacements)
                        if cell_value == '-':
                            continue

                        if format_type == 'number':
                            # Try to convert to number and set as numeric value
                            try:
                                if cell_value and str(cell_value).strip():
                                    numeric_value = float(str(cell_value))
                                    if numeric_value == int(numeric_value):
                                        cell.value = int(numeric_value)
                                    else:
                                        cell.value = numeric_value
                                    # Set number format
                                    cell.number_format = '0.00' if numeric_value != int(numeric_value) else '0'
                            except (ValueError, TypeError):
                                # If conversion fails, keep as text
                                cell.value = str(cell_value)

                        elif format_type == 'number (%)':
                            # Handle percentage format
                            try:
                                if cell_value and str(cell_value).strip():
                                    # Check if the value already contains % symbol
                                    if '%' in str(cell_value):
                                        # Remove % symbol and convert to decimal for Excel
                                        value_str = str(cell_value).replace('%', '')
                                        numeric_value = float(value_str) / 100  # Convert percentage to decimal
                                        cell.value = numeric_value
                                    else:
                                        # Value is already a decimal, use as-is
                                        numeric_value = float(str(cell_value))
                                        cell.value = numeric_value

                                    # Set percentage format (Excel will display as percentage)
                                    cell.number_format = '0.00%'
                            except (ValueError, TypeError):
                                # If conversion fails, keep as text
                                cell.value = str(cell_value)

                        elif format_type == 'text':
                            # Ensure it's stored as text
                            cell.value = str(cell_value)
                            # Set text format to prevent Excel from auto-converting
                            cell.number_format = '@'

                        elif format_type == 'date':
                            # Handle date format - convert to proper Excel date without changing the data
                            try:
                                if cell_value and str(cell_value).strip() and str(cell_value).strip() != '-':
                                    from datetime import datetime
                                    import re

                                    date_str = str(cell_value).strip()
                                    date_obj = None

                                    # Try to parse various date formats
                                    date_formats = [
                                        '%m/%d/%Y %I:%M:%S %p',  # 7/11/2025 12:00:00 AM
                                        '%Y-%m-%d %H:%M:%S',     # 2025-07-11 00:00:00
                                        '%d-%b-%y',              # 31-JUL-25
                                        '%d-%b-%Y',              # 31-JUL-2025
                                        '%Y-%m-%d',              # 2025-07-11
                                        '%m/%d/%Y',              # 7/11/2025
                                        '%d/%m/%Y'               # 11/07/2025
                                    ]

                                    for fmt in date_formats:
                                        try:
                                            date_obj = datetime.strptime(date_str, fmt)
                                            break
                                        except ValueError:
                                            continue

                                    if date_obj:
                                        # Set as proper Excel date object
                                        cell.value = date_obj
                                        # Use a date format that preserves the original appearance
                                        if 'AM' in date_str or 'PM' in date_str:
                                            cell.number_format = 'M/D/YYYY H:MM:SS AM/PM'
                                        else:
                                            cell.number_format = 'M/D/YYYY'
                                    else:
                                        # If parsing fails, keep as text but mark as date format
                                        cell.value = str(cell_value)
                                        cell.number_format = '@'
                                else:
                                    # Empty or dash values - keep as text
                                    cell.value = str(cell_value)
                                    cell.number_format = '@'
                            except Exception as e:
                                # If any error occurs, keep as text
                                cell.value = str(cell_value)
                                cell.number_format = '@'

            self.logger.info(f"Applied Excel cell formatting for {len(format_info)} columns")

        except Exception as e:
            self.logger.warning(f"Error applying Excel cell formatting: {e}")

    def _remove_duplicate_headers_from_output(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate headers from output DataFrame accounting for Exchange column shift"""
        try:
            if df.empty or len(df) < 1:
                return df

            # Get column names and first row values
            column_names = [str(col).strip() for col in df.columns]
            first_row = df.iloc[0].astype(str).tolist()

            # Check if first row contains headers (excluding Exchange column)
            # This handles the case where:
            # Columns: Exchange | Date | Symbol | Expiry Date | ...
            # Row 0:   MCX      | Date | Symbol | Expiry Date | ... (duplicate header from CSV)
            if len(column_names) > 1 and len(first_row) > 1:
                # Compare columns 1+ with first row 1+ (skip Exchange column)
                if column_names[1:] == [str(val).strip() for val in first_row[1:]]:
                    self.logger.info("Detected duplicate headers in output (Exchange column shift) - removing the first row")
                    # Remove the first row (index 0) as it's a duplicate header from original CSV
                    df_cleaned = df.drop(df.index[0]).reset_index(drop=True)
                    return df_cleaned

            # Fallback: Check if the first row contains the same values as column names (exact match)
            first_row_values = [str(val).strip() for val in first_row]
            if column_names == first_row_values:
                self.logger.info("Detected exact duplicate headers in output - removing the first data row")
                # Remove the first row (index 0) as it's a duplicate header
                df_cleaned = df.drop(df.index[0]).reset_index(drop=True)
                return df_cleaned

            # Additional check: if first two rows are identical
            if len(df) >= 2:
                second_row = df.iloc[1].astype(str).tolist()
                if first_row == second_row:
                    self.logger.info("Detected identical duplicate rows in output - removing the second row")
                    df_cleaned = df.drop(df.index[1]).reset_index(drop=True)
                    return df_cleaned

            # No duplicate headers detected
            return df

        except Exception as e:
            self.logger.warning(f"Error checking for duplicate headers in output: {e}")
            return df


class DataProcessingPipeline:
    """Main pipeline orchestrator"""

    def __init__(self, master_excel_path: str, output_dir: str = "output", enable_formatting: bool = True):
        self.master_excel_path = master_excel_path
        self.master_excel_reader = MasterExcelReader(master_excel_path)
        self.data_reader = DataReader()
        # Read replacement rules once when initializing the pipeline
        replacement_map = self.master_excel_reader.read_replacement_rules()
        self.transformation_engine = TransformationEngine(replacement_map)
        self.output_generator = OutputGenerator(output_dir)
        self.enable_formatting = enable_formatting
        self.logger = logging.getLogger(self.__class__.__name__)

        # Results tracking
        self.results: List[ProcessingResult] = []

    def process_all_files(self) -> List[ProcessingResult]:
        """Process all files listed in the master Excel"""

        self.logger.info("Starting data processing pipeline")

        try:
            # Read file list
            files = self.master_excel_reader.read_file_list()
            self.logger.info(f"Found {len(files)} files to process")

            # Process each file
            for file_info in files:
                result = self.process_single_file(file_info)
                self.results.append(result)

            # Generate summary report
            self._generate_summary_report()

            # Apply post-processing formatting if enabled
            if self.enable_formatting:
                self._apply_post_processing_formatting()

            self.logger.info("Pipeline processing completed")
            return self.results

        except Exception as e:
            self.logger.error(f"Pipeline error: {e}")
            raise

    def process_single_file(self, file_info: FileInfo) -> ProcessingResult:
        """Process a single file"""

        self.logger.info(f"Processing file: {file_info.file_name} (SR {file_info.sr_number})")

        errors = []
        columns_dropped = []
        columns_transformed = []
        output_file = None

        try:
            # Read transformation rules
            rules = self.master_excel_reader.read_transformation_rules(file_info.sr_number)

            if not rules:
                errors.append(f"No transformation rules found for SR {file_info.sr_number}")
                return ProcessingResult(
                    sr_number=file_info.sr_number,
                    file_name=file_info.file_name,
                    status="Failed",
                    errors=errors,
                    columns_dropped=[],
                    columns_transformed=[],
                    timestamp=datetime.now()
                )

            # Read the data file
            df = self.data_reader.read_file(file_info.file_location)

            # Apply transformations
            df_transformed, columns_dropped, columns_transformed = self.transformation_engine.apply_transformations(df, rules, file_info)

            # Extract format information for required columns
            format_info = {}
            for rule in rules:
                if rule.required and rule.format_type:
                    format_info[rule.header_name] = rule.format_type

            # Generate output file with format information
            output_file = self.output_generator.generate_output_file(
                df_transformed, file_info, columns_dropped, columns_transformed, format_info
            )

            self.logger.info(f"Successfully processed {file_info.file_name}")

            return ProcessingResult(
                sr_number=file_info.sr_number,
                file_name=file_info.file_name,
                status="Success",
                errors=[],
                columns_dropped=columns_dropped,
                columns_transformed=columns_transformed,
                timestamp=datetime.now(),
                output_file=output_file
            )

        except Exception as e:
            error_msg = f"Error processing {file_info.file_name}: {str(e)}"
            self.logger.error(error_msg)
            errors.append(error_msg)

            return ProcessingResult(
                sr_number=file_info.sr_number,
                file_name=file_info.file_name,
                status="Failed",
                errors=errors,
                columns_dropped=columns_dropped,
                columns_transformed=columns_transformed,
                timestamp=datetime.now(),
                output_file=output_file
            )

    def _generate_summary_report(self):
        """Generate a summary report of all processing results"""

        try:
            summary_data = []

            for result in self.results:
                summary_data.append({
                    'SR Number': result.sr_number,
                    'File Name': result.file_name,
                    'Status': result.status,
                    'Errors': '; '.join(result.errors) if result.errors else 'None',
                    'Columns Dropped': '; '.join(result.columns_dropped) if result.columns_dropped else 'None',
                    'Columns Transformed': '; '.join(result.columns_transformed) if result.columns_transformed else 'None',
                    'Processing Time': result.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    'Output File': result.output_file or 'N/A'
                })

            summary_df = pd.DataFrame(summary_data)

            # Generate summary statistics
            total_files = len(self.results)
            successful_files = len([r for r in self.results if r.status == "Success"])
            failed_files = total_files - successful_files

            stats_data = {
                'Metric': ['Total Files', 'Successful', 'Failed', 'Success Rate'],
                'Value': [total_files, successful_files, failed_files, f"{(successful_files/total_files)*100:.1f}%" if total_files > 0 else "0%"]
            }
            stats_df = pd.DataFrame(stats_data)

            # Save summary report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_path = self.output_generator.output_dir / f"processing_summary_{timestamp}.xlsx"

            with pd.ExcelWriter(summary_path, engine='openpyxl') as writer:
                summary_df.to_excel(writer, sheet_name='Processing Results', index=False)
                stats_df.to_excel(writer, sheet_name='Summary Statistics', index=False)

                # Format the sheets
                for sheet_name in writer.sheets:
                    sheet = writer.sheets[sheet_name]

                    # Auto-adjust column widths
                    for column in sheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter

                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass

                        adjusted_width = min(max_length + 2, 50)
                        sheet.column_dimensions[column_letter].width = adjusted_width

                    # Format header row
                    from openpyxl.styles import Font, PatternFill
                    header_font = Font(bold=True)
                    header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                    for cell in sheet[1]:
                        cell.font = header_font
                        cell.fill = header_fill

            self.logger.info(f"Generated summary report: {summary_path}")

        except Exception as e:
            self.logger.error(f"Error generating summary report: {e}")

    def _apply_post_processing_formatting(self):
        """Apply post-processing formatting to all generated Excel files"""
        try:
            self.logger.info("Starting post-processing data formatting")

            # Import the formatter (lazy import to avoid circular dependencies)
            try:
                from data_formatter import format_pipeline_output
            except ImportError as e:
                self.logger.warning(f"Data formatter not available: {e}")
                self.logger.warning("Skipping post-processing formatting")
                return

            # Apply formatting to all output files
            formatting_results = format_pipeline_output(self.master_excel_path, str(self.output_generator.output_dir))

            # Log formatting results
            successful_formatting = len([r for r in formatting_results if r.status in ["Success", "Partial"]])
            total_formatting = len(formatting_results)

            self.logger.info(f"Post-processing formatting completed: {successful_formatting}/{total_formatting} files formatted successfully")

            # Add formatting information to processing results
            for result in self.results:
                if result.output_file:
                    # Find corresponding formatting result
                    output_filename = Path(result.output_file).name
                    for fmt_result in formatting_results:
                        if Path(fmt_result.file_path).name == output_filename:
                            if fmt_result.status in ["Success", "Partial"]:
                                self.logger.info(f"Applied formatting to {output_filename}: {', '.join(fmt_result.columns_formatted)}")
                            else:
                                self.logger.warning(f"Formatting failed for {output_filename}: {'; '.join(fmt_result.errors)}")
                            break

        except Exception as e:
            self.logger.error(f"Error in post-processing formatting: {e}")
            self.logger.warning("Continuing without post-processing formatting")

    def enable_post_processing_formatting(self, enable: bool = True):
        """Enable or disable post-processing formatting"""
        self.enable_formatting = enable
        self.logger.info(f"Post-processing formatting {'enabled' if enable else 'disabled'}")

    def format_existing_files(self, file_paths: List[str] = None):
        """Format existing Excel files in the output directory"""
        try:
            self.logger.info("Formatting existing Excel files")

            # Import the formatter
            try:
                from data_formatter import PostProcessingManager
            except ImportError as e:
                self.logger.error(f"Data formatter not available: {e}")
                raise

            # Create formatting manager
            manager = PostProcessingManager(self.master_excel_path, str(self.output_generator.output_dir))

            if file_paths:
                # Format specific files
                results = manager.format_specific_files(file_paths)
            else:
                # Format all files in output directory
                results = manager.format_all_output_files()

            # Log results
            successful = len([r for r in results if r.status in ["Success", "Partial"]])
            total = len(results)

            self.logger.info(f"File formatting completed: {successful}/{total} files formatted successfully")

            return results

        except Exception as e:
            self.logger.error(f"Error formatting existing files: {e}")
            raise


# Example usage and testing
if __name__ == "__main__":
    import sys
    import argparse
    import json
    from pathlib import Path

    def get_master_file_path():
        """Get master Excel file path from various sources"""

        # Method 1: Command line argument
        if len(sys.argv) > 1:
            master_path = sys.argv[1]
            if Path(master_path).exists():
                print(f"Using master file from command line: {master_path}")
                return master_path
            else:
                print(f"Warning: Command line file '{master_path}' not found, trying other methods...")

        # Method 2: Configuration file
        config_file = Path("config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    master_path = config.get('master_excel_path')
                    if master_path and Path(master_path).exists():
                        print(f"Using master file from config.json: {master_path}")
                        return master_path
                    else:
                        print(f"Warning: Config file path '{master_path}' not found, trying other methods...")
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Warning: Error reading config.json: {e}, trying other methods...")

        # Method 3: Interactive prompt
        print("\nNo valid master Excel file found via command line or config.")
        print("Please enter the path to your master Excel file:")

        while True:
            master_path = input("Master Excel file path: ").strip().strip('"\'')
            if not master_path:
                print("Please enter a valid path.")
                continue

            if Path(master_path).exists():
                print(f"Using master file: {master_path}")
                return master_path
            else:
                print(f"File '{master_path}' not found. Please try again.")

                # Offer to create a sample config file
                create_config = input("Would you like to create a sample config.json file? (y/n): ").strip().lower()
                if create_config in ['y', 'yes']:
                    sample_config = {
                        "master_excel_path": "BRD file Automation.xlsx",
                        "output_dir": "output",
                        "log_level": "INFO"
                    }
                    with open("config.json", 'w') as f:
                        json.dump(sample_config, f, indent=2)
                    print("Created sample config.json file. Please edit it with your file paths.")
                    return None

    def main():
        """Main function for testing the pipeline"""

        print("Data Processing Pipeline")
        print("=" * 50)

        # Get master Excel file path dynamically
        master_excel_path = get_master_file_path()
        if not master_excel_path:
            print("No valid master Excel file provided. Exiting.")
            return 1

        # Configuration
        output_dir = "output"
        enable_formatting = True

        # Check if config file exists for output directory and formatting settings
        config_file = Path("config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    output_dir = config.get('output_dir', output_dir)
                    enable_formatting = config.get('enable_post_processing_formatting', enable_formatting)
            except:
                pass  # Use default if config reading fails

        try:
            # Create and run pipeline
            print(f"\nInitializing pipeline...")
            print(f"Master Excel: {master_excel_path}")
            print(f"Output directory: {output_dir}")
            print(f"Post-processing formatting: {'Enabled' if enable_formatting else 'Disabled'}")

            pipeline = DataProcessingPipeline(master_excel_path, output_dir, enable_formatting)
            results = pipeline.process_all_files()

            # Print summary
            print("\n" + "="*50)
            print("PROCESSING SUMMARY")
            print("="*50)

            total_files = len(results)
            successful_files = len([r for r in results if r.status == "Success"])
            failed_files = total_files - successful_files

            print(f"Total files processed: {total_files}")
            print(f"Successful: {successful_files}")
            print(f"Failed: {failed_files}")
            print(f"Success rate: {(successful_files/total_files)*100:.1f}%" if total_files > 0 else "0%")

            print("\nDetailed Results:")
            for result in results:
                status_symbol = "✓" if result.status == "Success" else "✗"
                print(f"{status_symbol} SR {result.sr_number}: {result.file_name} - {result.status}")
                if result.errors:
                    for error in result.errors:
                        print(f"    Error: {error}")

            print(f"\nOutput files saved to: {output_dir}/")
            print("Check the processing summary Excel file for detailed results.")

            return 0 if failed_files == 0 else 1

        except Exception as e:
            print(f"Pipeline failed: {e}")
            return 1

    sys.exit(main())
