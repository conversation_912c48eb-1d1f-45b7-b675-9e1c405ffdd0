import pandas as pd
from pathlib import Path
import logging
import sys

# Add the parent directory to the sys.path to import data_pipeline
sys.path.append(str(Path(__file__).parent))

from data_pipeline import MasterExcelReader, TransformationEngine, FileInfo, TransformationRule

# Configure logging for the test script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_replacement_test(master_excel_path: str, sr_number_to_test: str, column_to_test: str, sample_data: list):
    """
    Runs a test to verify the word replacement logic.
    """
    logger.info(f"Starting replacement test for SR: {sr_number_to_test}, Column: {column_to_test}")

    try:
        # 1. Initialize MasterExcelReader and read replacement rules
        reader = MasterExcelReader(master_excel_path)
        replacement_map = reader.read_replacement_rules()
        logger.info(f"Loaded replacement map: {replacement_map}")

        if not replacement_map:
            logger.warning("No replacement rules found. Test cannot proceed effectively.")
            return

        # 2. Initialize TransformationEngine with the loaded replacement map
        engine = TransformationEngine(replacement_map)

        # 3. Create a dummy DataFrame for testing
        df_test = pd.DataFrame({
            column_to_test: sample_data,
            'OtherColumn': ['A', 'B', 'C', 'D', 'E']
        })
        logger.info("\n--- Original DataFrame ---")
        logger.info(df_test)

        # 4. Create a dummy FileInfo to trigger replacement
        file_info_dummy = FileInfo(
            sr_number=sr_number_to_test,
            file_name="test_file.txt",
            file_location="dummy/path/test_file.txt",
            action_required="replace" # This triggers the replacement logic
        )

        # 5. Apply transformations (which includes word replacement if action_required is 'replace')
        # We need to pass dummy rules for apply_transformations to work, even if not used for replacement
        dummy_rules = [
            TransformationRule(header_name=column_to_test, required=True, format_type="text", special_action=""),
            TransformationRule(header_name="OtherColumn", required=True, format_type="text", special_action="")
        ]
        
        # The apply_transformations method expects a list of rules and will filter columns based on them.
        # Ensure the columns in df_test are covered by dummy_rules.
        df_transformed, _, _ = engine.apply_transformations(df_test, dummy_rules, file_info_dummy)

        logger.info("\n--- Transformed DataFrame (after replacement) ---")
        logger.info(df_transformed)

        # 6. Verify results
        # You can add assertions here to programmatically check if replacements happened as expected.
        # For now, we'll rely on visual inspection of the logged DataFrames.
        logger.info("\nReplacement test completed. Please inspect the DataFrames above.")

    except Exception as e:
        logger.error(f"An error occurred during the test: {e}")

if __name__ == "__main__":
    # Define the path to your master Excel file
    # Make sure this path is correct relative to where you run the script
    master_excel_file = "Exchange/BRD_Automation_RAW.xlsx"

    # Define the SR number and a column that should have replacement rules
    # You need to check your BRD_Automation_RAW.xlsx for actual values
    # For example, if SR. No. 1 has 'replace' in 'Action Required' and
    # one of its columns (e.g., 'Product') has values that should be replaced.
    sr_to_test = "1" # Replace with an actual SR number from your Sheet1 that has 'replace' in 'Action Required'
    col_to_test = "Product" # Replace with an actual column name from your SR sheet that contains values to be replaced

    # Sample data for the column to test. Include values that should be replaced
    # and values that should trigger the "not found" warning.
    sample_data_for_test = [
        "OldValue1",  # Should be replaced if in Replace_Rules
        "OldValue2",  # Should be replaced if in Replace_Rules
        "NonExistentValue", # Should trigger a warning
        "AnotherValue", # Should be replaced if in Replace_Rules
        "YetAnotherNonExistent" # Should trigger a warning
    ]

    # Run the test
    run_replacement_test(master_excel_file, sr_to_test, col_to_test, sample_data_for_test)
