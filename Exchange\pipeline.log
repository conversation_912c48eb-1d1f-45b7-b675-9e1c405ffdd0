2025-07-16 16:19:42,376 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 16:19:42,430 - MasterExcelReader - INFO - Reading replacement rules from sheet 'Replace_Rules', header row: 1
2025-07-16 16:19:42,432 - <PERSON>ExcelReader - INFO - Read 4 replacement rules from 'Replace_Rules'
2025-07-16 16:19:42,432 - __main__ - INFO - Loaded replacement map: {'SO': 'OPTSTK', 'IO': 'OPTIDX', 'SF': 'FUTSTK', 'IF': 'FUTIDX'}
2025-07-16 16:19:42,433 - __main__ - INFO - 
--- Original DataFrame ---
2025-07-16 16:19:42,433 - __main__ - INFO -                  Product OtherColumn
0              OldValue1           A
1              OldValue2           B
2       NonExistentValue           C
3           AnotherValue           D
4  YetAnotherNonExistent           E
2025-07-16 16:19:43,750 - TransformationEngine - INFO - Applied format conversions: Product -> text, OtherColumn -> text
2025-07-16 16:19:43,750 - TransformationEngine - INFO - Applied transformations: 0 columns transformed, 0 columns dropped
2025-07-16 16:19:43,751 - TransformationEngine - INFO - Final columns: ['Product', 'OtherColumn']
2025-07-16 16:19:43,751 - __main__ - INFO - 
--- Transformed DataFrame (after replacement) ---
2025-07-16 16:19:43,751 - __main__ - INFO -                  Product OtherColumn
0              OldValue1           A
1              OldValue2           B
2       NonExistentValue           C
3           AnotherValue           D
4  YetAnotherNonExistent           E
2025-07-16 16:19:43,755 - __main__ - INFO - 
Replacement test completed. Please inspect the DataFrames above.
2025-07-16 16:21:47,516 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 16:21:47,540 - MasterExcelReader - INFO - Reading replacement rules from sheet 'Replace_Rules', header row: 1
2025-07-16 16:21:47,542 - MasterExcelReader - INFO - Read 4 replacement rules from 'Replace_Rules'
2025-07-16 16:21:47,542 - __main__ - INFO - Loaded replacement map: {'SO': 'OPTSTK', 'IO': 'OPTIDX', 'SF': 'FUTSTK', 'IF': 'FUTIDX'}
2025-07-16 16:21:47,543 - __main__ - INFO - 
--- Original DataFrame ---
2025-07-16 16:21:47,543 - __main__ - INFO -                  Product OtherColumn
0              OldValue1           A
1              OldValue2           B
2       NonExistentValue           C
3           AnotherValue           D
4  YetAnotherNonExistent           E
2025-07-16 16:21:47,553 - TransformationEngine - INFO - Applying global word replacement for file: test_file.txt
2025-07-16 16:21:47,553 - TransformationEngine - WARNING - Replacement Error: Value 'OldValue1' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,553 - TransformationEngine - WARNING - Replacement Error: Value 'OldValue2' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'NonExistentValue' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'AnotherValue' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'YetAnotherNonExistent' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'A' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'B' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,554 - TransformationEngine - WARNING - Replacement Error: Value 'C' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,555 - TransformationEngine - WARNING - Replacement Error: Value 'D' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,555 - TransformationEngine - WARNING - Replacement Error: Value 'E' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:21:47,556 - TransformationEngine - INFO - Applied format conversions: Product -> text, OtherColumn -> text
2025-07-16 16:21:47,556 - TransformationEngine - INFO - Applied transformations: 0 columns transformed, 0 columns dropped
2025-07-16 16:21:47,556 - TransformationEngine - INFO - Final columns: ['Product', 'OtherColumn']
2025-07-16 16:21:47,556 - __main__ - INFO - 
--- Transformed DataFrame (after replacement) ---
2025-07-16 16:21:47,556 - __main__ - INFO -                  Product OtherColumn
0              OldValue1           A
1              OldValue2           B
2       NonExistentValue           C
3           AnotherValue           D
4  YetAnotherNonExistent           E
2025-07-16 16:21:47,560 - __main__ - INFO - 
Replacement test completed. Please inspect the DataFrames above.
2025-07-16 16:22:14,418 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 16:22:14,441 - MasterExcelReader - INFO - Reading replacement rules from sheet 'Replace_Rules', header row: 1
2025-07-16 16:22:14,444 - MasterExcelReader - INFO - Read 4 replacement rules from 'Replace_Rules'
2025-07-16 16:22:14,444 - __main__ - INFO - Loaded replacement map: {'SO': 'OPTSTK', 'IO': 'OPTIDX', 'SF': 'FUTSTK', 'IF': 'FUTIDX'}
2025-07-16 16:22:14,445 - __main__ - INFO - 
--- Original DataFrame ---
2025-07-16 16:22:14,445 - __main__ - INFO -                  Product OtherColumn
0                     SO           A
1                     IO           B
2       NonExistentValue           C
3                     SF           D
4  YetAnotherNonExistent           E
2025-07-16 16:22:14,452 - TransformationEngine - INFO - Applying global word replacement for file: test_file.txt
2025-07-16 16:22:14,452 - TransformationEngine - WARNING - Replacement Error: Value 'NonExistentValue' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,452 - TransformationEngine - WARNING - Replacement Error: Value 'YetAnotherNonExistent' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,453 - TransformationEngine - WARNING - Replacement Error: Value 'A' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,453 - TransformationEngine - WARNING - Replacement Error: Value 'B' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,453 - TransformationEngine - WARNING - Replacement Error: Value 'C' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,453 - TransformationEngine - WARNING - Replacement Error: Value 'D' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,453 - TransformationEngine - WARNING - Replacement Error: Value 'E' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 16:22:14,454 - TransformationEngine - INFO - Applied format conversions: Product -> text, OtherColumn -> text
2025-07-16 16:22:14,454 - TransformationEngine - INFO - Applied transformations: 0 columns transformed, 0 columns dropped
2025-07-16 16:22:14,454 - TransformationEngine - INFO - Final columns: ['Product', 'OtherColumn']
2025-07-16 16:22:14,454 - __main__ - INFO - 
--- Transformed DataFrame (after replacement) ---
2025-07-16 16:22:14,454 - __main__ - INFO -                  Product OtherColumn
0                 OPTSTK           A
1                 OPTIDX           B
2       NonExistentValue           C
3                 FUTSTK           D
4  YetAnotherNonExistent           E
2025-07-16 16:22:14,458 - __main__ - INFO - 
Replacement test completed. Please inspect the DataFrames above.
