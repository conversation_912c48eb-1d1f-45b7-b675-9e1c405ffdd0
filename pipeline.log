2025-07-16 13:40:27,954 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 13:40:27,982 - <PERSON><PERSON><PERSON><PERSON><PERSON>eader - WARNING - Could not find header row ('value', 'replace_with') in sheet 'Replace_Rules'. No word replacements will be applied.
2025-07-16 13:40:27,982 - __main__ - INFO - Loaded replacement map: {}
2025-07-16 13:40:27,982 - __main__ - WARNING - No replacement rules found. Test cannot proceed effectively.
2025-07-16 13:41:01,615 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 13:41:01,640 - MasterExcelReader - WARNING - Could not find header row ('value', 'replace_with') in sheet 'Replace_Rules'. No word replacements will be applied.
2025-07-16 13:41:01,642 - __main__ - INFO - Loaded replacement map: {}
2025-07-16 13:41:01,642 - __main__ - WARNING - No replacement rules found. Test cannot proceed effectively.
2025-07-16 13:41:33,220 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 13:41:33,248 - MasterExcelReader - WARNING - Could not find header row ('value', 'replace_with') in sheet 'Replace_Rules'. No word replacements will be applied.
2025-07-16 13:41:33,248 - __main__ - INFO - Loaded replacement map: {}
2025-07-16 13:41:33,249 - __main__ - WARNING - No replacement rules found. Test cannot proceed effectively.
2025-07-16 13:42:40,418 - __main__ - INFO - Starting replacement test for SR: 1, Column: Product
2025-07-16 13:42:40,455 - MasterExcelReader - INFO - Read 3 replacement rules from 'Replace_Rules'
2025-07-16 13:42:40,455 - __main__ - INFO - Loaded replacement map: {'OldValue1': 'NewValue1', 'OldValue2': 'NewValue2', 'AnotherValue': 'ReplacedAnother'}
2025-07-16 13:42:40,456 - __main__ - INFO - 
--- Original DataFrame ---
2025-07-16 13:42:40,456 - __main__ - INFO -                  Product OtherColumn
0              OldValue1           A
1              OldValue2           B
2       NonExistentValue           C
3           AnotherValue           D
4  YetAnotherNonExistent           E
2025-07-16 13:42:40,468 - TransformationEngine - INFO - Applying global word replacement for file: test_file.txt
2025-07-16 13:42:40,468 - TransformationEngine - WARNING - Replacement Error: Value 'NonExistentValue' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,468 - TransformationEngine - WARNING - Replacement Error: Value 'YetAnotherNonExistent' not found in Replace Sheet for column 'Product'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,469 - TransformationEngine - WARNING - Replacement Error: Value 'A' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,469 - TransformationEngine - WARNING - Replacement Error: Value 'B' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,469 - TransformationEngine - WARNING - Replacement Error: Value 'C' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,469 - TransformationEngine - WARNING - Replacement Error: Value 'D' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,469 - TransformationEngine - WARNING - Replacement Error: Value 'E' not found in Replace Sheet for column 'OtherColumn'. Please check and update the Replace Sheet accordingly.
2025-07-16 13:42:40,470 - TransformationEngine - INFO - Applied format conversions: Product -> text, OtherColumn -> text
2025-07-16 13:42:40,470 - TransformationEngine - INFO - Applied transformations: 0 columns transformed, 0 columns dropped
2025-07-16 13:42:40,470 - TransformationEngine - INFO - Final columns: ['Product', 'OtherColumn']
2025-07-16 13:42:40,470 - __main__ - INFO - 
--- Transformed DataFrame (after replacement) ---
2025-07-16 13:42:40,470 - __main__ - INFO -                  Product OtherColumn
0              NewValue1           A
1              NewValue2           B
2       NonExistentValue           C
3        ReplacedAnother           D
4  YetAnotherNonExistent           E
2025-07-16 13:42:40,475 - __main__ - INFO - 
Replacement test completed. Please inspect the DataFrames above.
